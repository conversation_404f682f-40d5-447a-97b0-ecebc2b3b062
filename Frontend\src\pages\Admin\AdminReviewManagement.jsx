import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { toast } from "react-toastify";
import AdminLayout from "../../components/admin/AdminLayout";
import {
  selectReviews,
  selectSelectedReviews,
  selectReviewFilters,
  selectReviewsPagination,
  selectLoading,
  selectErrors,
  setSelectedReviews,
  setReviewFilters,
} from "../../redux/slices/adminDashboardSlice";
import {
  fetchReviews,
  deleteReview,
  bulkDeleteReviews,
  getReviewStats,
} from "../../redux/slices/adminDashboardThunks";
import "../../styles/AdminReviewManagement.css";
import { FaSearch, FaFilter, FaEye, FaEdit, FaTrash, FaCheck, FaTimes, FaStar } from "react-icons/fa";

const AdminReviewManagement = () => {
    const dispatch = useDispatch();

    // Redux state
    const reviews = useSelector(selectReviews);
    const selectedReviews = useSelector(selectSelectedReviews);
    const filters = useSelector(selectReviewFilters);
    const pagination = useSelector(selectReviewsPagination);
    const loading = useSelector(selectLoading);
    const errors = useSelector(selectErrors);

    // Local state
    const [searchTerm, setSearchTerm] = useState(filters.search || "");
    const [statusFilter, setStatusFilter] = useState(filters.status || "all");
    const [ratingFilter, setRatingFilter] = useState(filters.rating || "all");
    const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
    const [reviewToDelete, setReviewToDelete] = useState(null);
    const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);

    // Fetch reviews on component mount and when filters change
    useEffect(() => {
        const params = {
            page: pagination.current,
            limit: pagination.limit,
            search: searchTerm,
            status: statusFilter === 'all' ? '' : statusFilter,
            rating: ratingFilter === 'all' ? '' : ratingFilter,
            sortBy: filters.sortBy,
            sortOrder: filters.sortOrder
        };

        dispatch(fetchReviews(params));
        dispatch(getReviewStats());
    }, [dispatch, pagination.current, pagination.limit, searchTerm, statusFilter, ratingFilter, filters.sortBy, filters.sortOrder]);

    // Handle search
    const handleSearch = (value) => {
        setSearchTerm(value);
        dispatch(setReviewFilters({ search: value }));
    };

    // Handle status filter
    const handleStatusFilter = (value) => {
        setStatusFilter(value);
        dispatch(setReviewFilters({ status: value }));
    };

    // Handle rating filter
    const handleRatingFilter = (value) => {
        setRatingFilter(value);
        dispatch(setReviewFilters({ rating: value }));
    };

    // Handle single review selection
    const handleReviewSelect = (reviewId) => {
        const isSelected = selectedReviews.includes(reviewId);
        if (isSelected) {
            dispatch(setSelectedReviews(selectedReviews.filter(id => id !== reviewId)));
        } else {
            dispatch(setSelectedReviews([...selectedReviews, reviewId]));
        }
    };

    // Handle select all reviews
    const handleSelectAll = () => {
        if (selectedReviews.length === reviews.data.length) {
            dispatch(setSelectedReviews([]));
        } else {
            dispatch(setSelectedReviews(reviews.data.map(review => review._id)));
        }
    };

    // Handle delete single review
    const handleDeleteReview = (review) => {
        setReviewToDelete(review);
        setShowDeleteConfirm(true);
    };

    // Confirm delete single review
    const confirmDeleteReview = async () => {
        if (reviewToDelete) {
            try {
                await dispatch(deleteReview(reviewToDelete._id)).unwrap();
                toast.success('Review deleted successfully');
                setShowDeleteConfirm(false);
                setReviewToDelete(null);
            } catch (error) {
                toast.error(error || 'Failed to delete review');
            }
        }
    };

    // Handle bulk delete
    const handleBulkDelete = () => {
        if (selectedReviews.length === 0) {
            toast.warning('Please select reviews to delete');
            return;
        }
        setShowBulkDeleteConfirm(true);
    };

    // Confirm bulk delete
    const confirmBulkDelete = async () => {
        try {
            await dispatch(bulkDeleteReviews(selectedReviews)).unwrap();
            toast.success(`${selectedReviews.length} reviews deleted successfully`);
            dispatch(setSelectedReviews([]));
            setShowBulkDeleteConfirm(false);
        } catch (error) {
            toast.error(error || 'Failed to delete reviews');
        }
    };

    // Format date
    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    // Render star rating
    const renderStarRating = (rating) => {
        const stars = [];
        for (let i = 1; i <= 5; i++) {
            stars.push(
                <FaStar
                    key={i}
                    className={i <= rating ? 'star-filled' : 'star-empty'}
                />
            );
        }
        return <div className="star-rating">{stars}</div>;
    };

    // Handle pagination
    const handlePageChange = (page) => {
        dispatch(setReviewFilters({ page }));
    };

    return (
        <AdminLayout>
            <div className="AdminReviewManagement">
                <div className="AdminReviewManagement__header">
                    <h1>Review Management</h1>
                    <p>Manage and monitor all content reviews</p>

                    {/* Bulk Actions */}
                    {selectedReviews.length > 0 && (
                        <div className="bulk-actions">
                            <span>{selectedReviews.length} reviews selected</span>
                            <button
                                className="bulk-delete-btn"
                                onClick={handleBulkDelete}
                            >
                                <FaTrash /> Delete Selected
                            </button>
                        </div>
                    )}
                </div>

                {/* Search and Filter Section */}
                <div className="AdminReviewManagement__controls">
                    <div className="search-box">
                        <FaSearch className="search-icon" />
                        <input
                            type="text"
                            placeholder="Search reviews..."
                            value={searchTerm}
                            onChange={(e) => handleSearch(e.target.value)}
                        />
                    </div>

                    <div className="filter-box">
                        <select
                            value={statusFilter}
                            onChange={(e) => handleStatusFilter(e.target.value)}
                        >
                            <option value="all">All Status</option>
                            <option value="pending">Pending</option>
                            <option value="approved">Approved</option>
                            <option value="rejected">Rejected</option>
                            <option value="flagged">Flagged</option>
                        </select>

                        <select
                            value={ratingFilter}
                            onChange={(e) => handleRatingFilter(e.target.value)}
                        >
                            <option value="all">All Ratings</option>
                            <option value="5">5 Stars</option>
                            <option value="4">4 Stars</option>
                            <option value="3">3 Stars</option>
                            <option value="2">2 Stars</option>
                            <option value="1">1 Star</option>
                        </select>
                    </div>
                </div>

                {/* Table Section */}
                <div className="AdminReviewManagement__table">
                    {loading.reviews ? (
                        <div className="loading">Loading reviews...</div>
                    ) : errors.reviews ? (
                        <div className="error">Error: {errors.reviews}</div>
                    ) : (
                        <table>
                            <thead>
                                <tr>
                                    <th>
                                        <input
                                            type="checkbox"
                                            checked={selectedReviews.length === reviews.data.length && reviews.data.length > 0}
                                            onChange={handleSelectAll}
                                        />
                                    </th>
                                    <th>Review ID</th>
                                    <th>Content</th>
                                    <th>Reviewer</th>
                                    <th>Rating</th>
                                    <th>Comment</th>
                                    <th>Status</th>
                                    <th>Created Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {reviews.data.length === 0 ? (
                                    <tr>
                                        <td colSpan="9" className="no-data">
                                            No reviews found
                                        </td>
                                    </tr>
                                ) : (
                                    reviews.data.map((review) => (
                                        <tr key={review._id}>
                                            <td>
                                                <input
                                                    type="checkbox"
                                                    checked={selectedReviews.includes(review._id)}
                                                    onChange={() => handleReviewSelect(review._id)}
                                                />
                                            </td>
                                            <td className="review-id">
                                                {review._id.slice(-8)}
                                            </td>
                                            <td className="content-info">
                                                <div className="content-title">
                                                    {review.content?.title || 'N/A'}
                                                </div>
                                                <div className="content-type">
                                                    {review.content?.contentType} • {review.content?.sport}
                                                </div>
                                            </td>
                                            <td className="reviewer-info">
                                                <div className="reviewer-name">
                                                    {review.user?.firstName} {review.user?.lastName}
                                                </div>
                                                {review.isVerifiedPurchase && (
                                                    <span className="verified-badge">
                                                        <FaCheck /> Verified Purchase
                                                    </span>
                                                )}
                                            </td>
                                            <td className="rating">
                                                {renderStarRating(review.rating)}
                                                <span className="rating-number">({review.rating})</span>
                                            </td>
                                            <td className="comment">
                                                <div className="comment-text">
                                                    {review.text.length > 100
                                                        ? `${review.text.substring(0, 100)}...`
                                                        : review.text
                                                    }
                                                </div>
                                            </td>
                                            <td className="status">
                                                <span className={`status-badge status-${review.status || 'approved'}`}>
                                                    {review.status || 'approved'}
                                                </span>
                                            </td>
                                            <td className="created-date">
                                                {formatDate(review.createdAt)}
                                            </td>
                                            <td className="actions">
                                                <button
                                                    className="action-btn view-btn"
                                                    title="View Details"
                                                >
                                                    <FaEye />
                                                </button>
                                                <button
                                                    className="action-btn delete-btn"
                                                    onClick={() => handleDeleteReview(review)}
                                                    title="Delete Review"
                                                >
                                                    <FaTrash />
                                                </button>
                                            </td>
                                        </tr>
                                    ))
                                )}
                            </tbody>
                        </table>
                    )}
                </div>

                {/* Pagination Section */}
                <div className="AdminReviewManagement__pagination">
                    <button
                        disabled={pagination.current <= 1}
                        onClick={() => handlePageChange(pagination.current - 1)}
                    >
                        Previous
                    </button>
                    <span>Page {pagination.current} of {pagination.pages}</span>
                    <button
                        disabled={pagination.current >= pagination.pages}
                        onClick={() => handlePageChange(pagination.current + 1)}
                    >
                        Next
                    </button>
                </div>

                {/* Delete Confirmation Modal */}
                {showDeleteConfirm && (
                    <div className="modal-overlay">
                        <div className="modal">
                            <h3>Confirm Delete</h3>
                            <p>Are you sure you want to delete this review? This action cannot be undone.</p>
                            <div className="modal-actions">
                                <button
                                    className="cancel-btn"
                                    onClick={() => setShowDeleteConfirm(false)}
                                >
                                    Cancel
                                </button>
                                <button
                                    className="delete-btn"
                                    onClick={confirmDeleteReview}
                                >
                                    Delete
                                </button>
                            </div>
                        </div>
                    </div>
                )}

                {/* Bulk Delete Confirmation Modal */}
                {showBulkDeleteConfirm && (
                    <div className="modal-overlay">
                        <div className="modal">
                            <h3>Confirm Bulk Delete</h3>
                            <p>Are you sure you want to delete {selectedReviews.length} selected reviews? This action cannot be undone.</p>
                            <div className="modal-actions">
                                <button
                                    className="cancel-btn"
                                    onClick={() => setShowBulkDeleteConfirm(false)}
                                >
                                    Cancel
                                </button>
                                <button
                                    className="delete-btn"
                                    onClick={confirmBulkDelete}
                                >
                                    Delete All
                                </button>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </AdminLayout>
    );
};

export default AdminReviewManagement; 